import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Link } from "react-router";
import { useState } from "react";

export default function FinanceDashboard() {
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).getTime(), // Start of year
    endDate: Date.now()
  });

  // Get key financial data
  const trialBalance = useQuery(api.trialBalance.generateTrialBalance, {
    endDate: dateRange.endDate,
    includeZeroBalances: false
  });

  const balanceSheet = useQuery(api.balanceSheet.generateBalanceSheet, {
    asOfDate: dateRange.endDate,
    includeZeroBalances: false
  });

  const incomeStatement = useQuery(api.incomeStatement.generateIncomeStatement, {
    startDate: dateRange.startDate,
    endDate: dateRange.endDate,
    includeZeroBalances: false
  });

  const apAging = useQuery(api.accountsPayable.getAgingSchedule, {
    asOfDate: dateRange.endDate,
    includeZeroBalances: false
  });

  const cashPosition = useQuery(api.cashBank.getCashPositionSummary, {
    asOfDate: dateRange.endDate
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-PH');
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Finance Dashboard</h1>
          <p className="text-gray-600">Overview of financial position and performance</p>
        </div>
        
        {/* Date Range Selector */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Period:</label>
            <input
              type="date"
              value={new Date(dateRange.startDate).toISOString().split('T')[0]}
              onChange={(e) => setDateRange(prev => ({ 
                ...prev, 
                startDate: new Date(e.target.value).getTime() 
              }))}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            />
            <span className="text-gray-500">to</span>
            <input
              type="date"
              value={new Date(dateRange.endDate).toISOString().split('T')[0]}
              onChange={(e) => setDateRange(prev => ({ 
                ...prev, 
                endDate: new Date(e.target.value).getTime() 
              }))}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            />
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Assets */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                <span className="text-blue-600 font-semibold">📊</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Assets</p>
              <p className="text-2xl font-semibold text-gray-900">
                {balanceSheet ? formatCurrency(balanceSheet.balanceSheet.totals.assets) : '...'}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <Link 
              to="/finance/balance-sheet" 
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              View Balance Sheet →
            </Link>
          </div>
        </div>

        {/* Net Income */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                <span className="text-green-600 font-semibold">💰</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Net Income</p>
              <p className={`text-2xl font-semibold ${
                incomeStatement && incomeStatement.incomeStatement.profitLoss.netIncome >= 0 
                  ? 'text-green-600' 
                  : 'text-red-600'
              }`}>
                {incomeStatement ? formatCurrency(incomeStatement.incomeStatement.profitLoss.netIncome) : '...'}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <Link 
              to="/finance/income-statement" 
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              View Income Statement →
            </Link>
          </div>
        </div>

        {/* Cash Position */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                <span className="text-yellow-600 font-semibold">🏦</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Cash Position</p>
              <p className="text-2xl font-semibold text-gray-900">
                {cashPosition ? formatCurrency(cashPosition.summary.totalCashPosition) : '...'}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <Link 
              to="/finance/cash-bank" 
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              View Cash Management →
            </Link>
          </div>
        </div>

        {/* Accounts Payable */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
                <span className="text-red-600 font-semibold">🧾</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Accounts Payable</p>
              <p className="text-2xl font-semibold text-gray-900">
                {apAging ? formatCurrency(apAging.summary.totalOutstanding) : '...'}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <Link 
              to="/finance/accounts-payable" 
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              View AP Aging →
            </Link>
          </div>
        </div>
      </div>

      {/* Charts and Tables Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Trial Balance Summary */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Trial Balance Summary</h3>
          </div>
          <div className="p-6">
            {trialBalance ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-500">Total Debits</span>
                  <span className="text-sm font-semibold text-gray-900">
                    {formatCurrency(trialBalance.summary.totalDebitBalances)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-500">Total Credits</span>
                  <span className="text-sm font-semibold text-gray-900">
                    {formatCurrency(trialBalance.summary.totalCreditBalances)}
                  </span>
                </div>
                <div className="flex justify-between items-center pt-2 border-t">
                  <span className="text-sm font-medium text-gray-700">Balance Status</span>
                  <span className={`text-sm font-semibold ${
                    trialBalance.summary.isBalanced ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {trialBalance.summary.isBalanced ? '✓ Balanced' : '✗ Out of Balance'}
                  </span>
                </div>
                <div className="pt-4">
                  <Link 
                    to="/finance/trial-balance"
                    className="text-sm text-blue-600 hover:text-blue-500"
                  >
                    View Full Trial Balance →
                  </Link>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500">Loading...</div>
            )}
          </div>
        </div>

        {/* AP Aging Summary */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">AP Aging Summary</h3>
          </div>
          <div className="p-6">
            {apAging ? (
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Current</span>
                  <span className="text-sm font-medium">
                    {formatCurrency(apAging.bucketTotals.current)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">1-30 Days</span>
                  <span className="text-sm font-medium text-yellow-600">
                    {formatCurrency(apAging.bucketTotals['1-30'])}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">31-60 Days</span>
                  <span className="text-sm font-medium text-orange-600">
                    {formatCurrency(apAging.bucketTotals['31-60'])}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Over 60 Days</span>
                  <span className="text-sm font-medium text-red-600">
                    {formatCurrency(apAging.bucketTotals['61-90'] + apAging.bucketTotals['over-90'])}
                  </span>
                </div>
                <div className="flex justify-between items-center pt-2 border-t">
                  <span className="text-sm font-medium text-gray-700">Total Outstanding</span>
                  <span className="text-sm font-semibold text-gray-900">
                    {formatCurrency(apAging.summary.totalOutstanding)}
                  </span>
                </div>
                <div className="pt-4">
                  <Link 
                    to="/finance/accounts-payable/aging"
                    className="text-sm text-blue-600 hover:text-blue-500"
                  >
                    View Full Aging Report →
                  </Link>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500">Loading...</div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              to="/finance/journal-entries/new"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl mr-3">📝</span>
              <div>
                <div className="font-medium text-gray-900">New Journal Entry</div>
                <div className="text-sm text-gray-500">Record a transaction</div>
              </div>
            </Link>
            
            <Link
              to="/finance/accounts-payable/new"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl mr-3">🧾</span>
              <div>
                <div className="font-medium text-gray-900">Record Invoice</div>
                <div className="text-sm text-gray-500">Add supplier invoice</div>
              </div>
            </Link>
            
            <Link
              to="/finance/cash-bank/transactions"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl mr-3">💸</span>
              <div>
                <div className="font-medium text-gray-900">Cash Transaction</div>
                <div className="text-sm text-gray-500">Record cash movement</div>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

import { type RouteConfig, index, route } from "@react-router/dev/routes";

export default [
  index("routes/home.tsx"),

  // Finance and Accounting Routes
  route("finance", "routes/finance/layout.tsx", [
    index("routes/finance/dashboard.tsx"),
    route("chart-of-accounts", "routes/finance/chart-of-accounts.tsx"),
    route("journal-entries", "routes/finance/journal-entries/layout.tsx", [
      index("routes/finance/journal-entries/list.tsx"),
      route("new", "routes/finance/journal-entries/new.tsx"),
      route(":entryId", "routes/finance/journal-entries/details.tsx"),
    ]),
    route("general-ledger", "routes/finance/general-ledger.tsx"),
    route("trial-balance", "routes/finance/trial-balance.tsx"),
    route("balance-sheet", "routes/finance/balance-sheet.tsx"),
    route("income-statement", "routes/finance/income-statement.tsx"),
    route("cash-flow", "routes/finance/cash-flow.tsx"),
    route("accounts-payable", "routes/finance/accounts-payable/layout.tsx", [
      index("routes/finance/accounts-payable/list.tsx"),
      route("new", "routes/finance/accounts-payable/new.tsx"),
      route("aging", "routes/finance/accounts-payable/aging.tsx"),
      route(":payableId", "routes/finance/accounts-payable/details.tsx"),
    ]),
    route("cash-bank", "routes/finance/cash-bank/layout.tsx", [
      index("routes/finance/cash-bank/dashboard.tsx"),
      route("transactions", "routes/finance/cash-bank/transactions.tsx"),
      route("reconciliation", "routes/finance/cash-bank/reconciliation.tsx"),
    ]),
    route("reports", "routes/finance/reports/layout.tsx", [
      index("routes/finance/reports/dashboard.tsx"),
      route("snapshots", "routes/finance/reports/snapshots.tsx"),
      route("comparative", "routes/finance/reports/comparative.tsx"),
    ]),
  ]),
] satisfies RouteConfig;

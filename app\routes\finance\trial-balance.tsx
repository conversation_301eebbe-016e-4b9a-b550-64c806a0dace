import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";

export default function TrialBalance() {
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).getTime(), // Start of year
    endDate: Date.now()
  });

  const [reportType, setReportType] = useState<'regular' | 'adjusted'>('regular');
  const [includeZeroBalances, setIncludeZeroBalances] = useState(false);

  const trialBalance = useQuery(
    reportType === 'regular' 
      ? api.trialBalance.generateTrialBalance 
      : api.trialBalance.generateAdjustedTrialBalance,
    {
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
      includeZeroBalances
    }
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-PH');
  };

  const getBalanceDisplay = (account: any) => {
    const balance = reportType === 'adjusted' ? account.adjustedBalance : account.balance;
    const isDebitBalance = ['asset', 'expense'].includes(account.account.type);
    
    if (balance === 0) {
      return { debit: 0, credit: 0 };
    }
    
    if (balance > 0) {
      return isDebitBalance 
        ? { debit: balance, credit: 0 }
        : { debit: 0, credit: balance };
    } else {
      return isDebitBalance 
        ? { debit: 0, credit: Math.abs(balance) }
        : { debit: Math.abs(balance), credit: 0 };
    }
  };

  const accountTypeOrder = ['asset', 'liability', 'equity', 'revenue', 'expense'];
  const accountTypeLabels = {
    asset: 'Assets',
    liability: 'Liabilities', 
    equity: 'Equity',
    revenue: 'Revenue',
    expense: 'Expenses'
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Trial Balance</h1>
          <p className="text-gray-600">Summary of all account balances</p>
        </div>
        
        {/* Export Button */}
        <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
          📊 Export
        </button>
      </div>

      {/* Controls */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Report Type</label>
            <select
              value={reportType}
              onChange={(e) => setReportType(e.target.value as 'regular' | 'adjusted')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="regular">Regular Trial Balance</option>
              <option value="adjusted">Adjusted Trial Balance</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
            <input
              type="date"
              value={new Date(dateRange.startDate).toISOString().split('T')[0]}
              onChange={(e) => setDateRange(prev => ({ 
                ...prev, 
                startDate: new Date(e.target.value).getTime() 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
            <input
              type="date"
              value={new Date(dateRange.endDate).toISOString().split('T')[0]}
              onChange={(e) => setDateRange(prev => ({ 
                ...prev, 
                endDate: new Date(e.target.value).getTime() 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
          <div className="flex items-end">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={includeZeroBalances}
                onChange={(e) => setIncludeZeroBalances(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">Include zero balances</span>
            </label>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      {trialBalance && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Total Accounts</div>
            <div className="text-2xl font-bold text-gray-900">{trialBalance.summary.totalAccounts}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Total Debits</div>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(trialBalance.summary.totalDebitBalances)}
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Total Credits</div>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(trialBalance.summary.totalCreditBalances)}
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Balance Status</div>
            <div className={`text-2xl font-bold ${
              trialBalance.summary.isBalanced ? 'text-green-600' : 'text-red-600'
            }`}>
              {trialBalance.summary.isBalanced ? '✓ Balanced' : '✗ Out of Balance'}
            </div>
            {!trialBalance.summary.isBalanced && (
              <div className="text-sm text-red-600">
                Difference: {formatCurrency(trialBalance.summary.difference)}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Trial Balance Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              {reportType === 'regular' ? 'Trial Balance' : 'Adjusted Trial Balance'}
            </h3>
            <div className="text-sm text-gray-500">
              As of {formatDate(dateRange.endDate)}
              {dateRange.startDate && (
                <span> (From {formatDate(dateRange.startDate)})</span>
              )}
            </div>
          </div>
        </div>

        {trialBalance ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Account Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Account Name
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Debit Balance
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Credit Balance
                  </th>
                  {reportType === 'adjusted' && (
                    <>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Adjustments
                      </th>
                    </>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {accountTypeOrder.map(accountType => {
                  const accountsOfType = (reportType === 'adjusted' 
                    ? trialBalance.adjustedTrialBalance 
                    : trialBalance.trialBalance
                  ).filter((item: any) => item.account.type === accountType);

                  if (accountsOfType.length === 0) return null;

                  return (
                    <React.Fragment key={accountType}>
                      {/* Account Type Header */}
                      <tr className="bg-gray-100">
                        <td colSpan={reportType === 'adjusted' ? 6 : 5} className="px-6 py-2">
                          <div className="font-semibold text-gray-900 uppercase text-sm">
                            {accountTypeLabels[accountType as keyof typeof accountTypeLabels]}
                          </div>
                        </td>
                      </tr>
                      
                      {/* Accounts */}
                      {accountsOfType.map((item: any) => {
                        const balanceDisplay = getBalanceDisplay(item);
                        return (
                          <tr key={item.account._id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                              {item.account.code}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {item.account.name}
                              {item.account.subtype && (
                                <div className="text-xs text-gray-500">
                                  {item.account.subtype.replace('_', ' ')}
                                </div>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                              <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                                {item.account.type}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-mono">
                              {balanceDisplay.debit > 0 ? formatCurrency(balanceDisplay.debit) : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-mono">
                              {balanceDisplay.credit > 0 ? formatCurrency(balanceDisplay.credit) : '-'}
                            </td>
                            {reportType === 'adjusted' && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-mono">
                                {item.netAdjustment !== 0 ? (
                                  <span className={item.netAdjustment > 0 ? 'text-green-600' : 'text-red-600'}>
                                    {item.netAdjustment > 0 ? '+' : ''}{formatCurrency(item.netAdjustment)}
                                  </span>
                                ) : '-'}
                              </td>
                            )}
                          </tr>
                        );
                      })}
                    </React.Fragment>
                  );
                })}
                
                {/* Totals Row */}
                <tr className="bg-gray-100 border-t-2 border-gray-300 font-semibold">
                  <td colSpan={3} className="px-6 py-4 text-right text-sm font-bold text-gray-900">
                    TOTALS:
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right">
                    {formatCurrency(trialBalance.summary.totalDebitBalances)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right">
                    {formatCurrency(trialBalance.summary.totalCreditBalances)}
                  </td>
                  {reportType === 'adjusted' && (
                    <td className="px-6 py-4"></td>
                  )}
                </tr>
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            Loading trial balance...
          </div>
        )}
      </div>

      {/* Validation Messages */}
      {trialBalance?.validation && (
        <div className="space-y-2">
          {trialBalance.validation.errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="text-red-400">⚠️</div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Errors Found:</h3>
                  <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                    {trialBalance.validation.errors.map((error: string, index: number) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
          
          {trialBalance.validation.warnings.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex">
                <div className="text-yellow-400">⚠️</div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">Warnings:</h3>
                  <ul className="mt-2 text-sm text-yellow-700 list-disc list-inside">
                    {trialBalance.validation.warnings.map((warning: string, index: number) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

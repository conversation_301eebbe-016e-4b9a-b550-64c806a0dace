import { Outlet, Link, useLocation } from "react-router";

const journalNavigation = [
  {
    name: "All Entries",
    href: "/finance/journal-entries",
    description: "View all journal entries"
  },
  {
    name: "New Entry",
    href: "/finance/journal-entries/new",
    description: "Create new journal entry"
  }
];

export default function JournalEntriesLayout() {
  const location = useLocation();

  const isActive = (href: string) => {
    if (href === "/finance/journal-entries") {
      return location.pathname === "/finance/journal-entries";
    }
    return location.pathname.startsWith(href);
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Journal Entries</h1>
        <p className="text-gray-600">Record and manage financial transactions</p>
      </div>

      {/* Sub Navigation */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          {journalNavigation.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={`
                pb-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${isActive(item.href)
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              {item.name}
            </Link>
          ))}
        </nav>
      </div>

      {/* Content */}
      <Outlet />
    </div>
  );
}

import { Outlet, Link, useLocation } from "react-router";

const apNavigation = [
  {
    name: "All Payables",
    href: "/finance/accounts-payable",
    description: "View all supplier invoices"
  },
  {
    name: "New Invoice",
    href: "/finance/accounts-payable/new",
    description: "Record new supplier invoice"
  },
  {
    name: "Aging Report",
    href: "/finance/accounts-payable/aging",
    description: "AP aging analysis"
  }
];

export default function AccountsPayableLayout() {
  const location = useLocation();

  const isActive = (href: string) => {
    if (href === "/finance/accounts-payable") {
      return location.pathname === "/finance/accounts-payable";
    }
    return location.pathname.startsWith(href);
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Accounts Payable</h1>
        <p className="text-gray-600">Manage supplier invoices and payments</p>
      </div>

      {/* Sub Navigation */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          {apNavigation.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={`
                pb-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${isActive(item.href)
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              {item.name}
            </Link>
          ))}
        </nav>
      </div>

      {/* Content */}
      <Outlet />
    </div>
  );
}

import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { useState } from "react";
import { useNavigate } from "react-router";

interface JournalLine {
  accountId: string;
  debit: number;
  credit: number;
  description: string;
}

export default function NewJournalEntry() {
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    description: '',
    reference: '',
    entryType: 'regular' as const
  });

  const [lines, setLines] = useState<JournalLine[]>([
    { accountId: '', debit: 0, credit: 0, description: '' },
    { accountId: '', debit: 0, credit: 0, description: '' }
  ]);

  const [errors, setErrors] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const accounts = useQuery(api.chartOfAccounts.getChartOfAccounts, {
    includeInactive: false
  });

  const postJournalEntry = useMutation(api.generalLedger.postJournalEntry);

  // Flatten accounts for dropdown
  const flattenAccounts = (accountTree: any[]): any[] => {
    const result: any[] = [];
    const traverse = (accounts: any[], prefix = '') => {
      accounts.forEach(account => {
        result.push({
          ...account,
          displayName: `${prefix}${account.code} - ${account.name}`
        });
        if (account.children && account.children.length > 0) {
          traverse(account.children, prefix + '  ');
        }
      });
    };
    traverse(accountTree);
    return result;
  };

  const flatAccounts = accounts ? flattenAccounts(accounts) : [];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const addLine = () => {
    setLines([...lines, { accountId: '', debit: 0, credit: 0, description: '' }]);
  };

  const removeLine = (index: number) => {
    if (lines.length > 2) {
      setLines(lines.filter((_, i) => i !== index));
    }
  };

  const updateLine = (index: number, field: keyof JournalLine, value: any) => {
    const newLines = [...lines];
    newLines[index] = { ...newLines[index], [field]: value };
    
    // If updating debit, clear credit and vice versa
    if (field === 'debit' && value > 0) {
      newLines[index].credit = 0;
    } else if (field === 'credit' && value > 0) {
      newLines[index].debit = 0;
    }
    
    setLines(newLines);
  };

  const calculateTotals = () => {
    const totalDebits = lines.reduce((sum, line) => sum + (line.debit || 0), 0);
    const totalCredits = lines.reduce((sum, line) => sum + (line.credit || 0), 0);
    return { totalDebits, totalCredits, difference: totalDebits - totalCredits };
  };

  const validateEntry = () => {
    const errors: string[] = [];
    const { totalDebits, totalCredits, difference } = calculateTotals();

    // Check if entry is balanced
    if (Math.abs(difference) > 0.01) {
      errors.push(`Entry is not balanced. Difference: ${formatCurrency(difference)}`);
    }

    // Check if all lines have accounts
    lines.forEach((line, index) => {
      if (!line.accountId) {
        errors.push(`Line ${index + 1}: Account is required`);
      }
      if (line.debit === 0 && line.credit === 0) {
        errors.push(`Line ${index + 1}: Either debit or credit amount is required`);
      }
      if (line.debit > 0 && line.credit > 0) {
        errors.push(`Line ${index + 1}: Cannot have both debit and credit amounts`);
      }
    });

    // Check if description is provided
    if (!formData.description.trim()) {
      errors.push('Entry description is required');
    }

    // Check if date is valid
    if (!formData.date) {
      errors.push('Entry date is required');
    }

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationErrors = validateEntry();
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors([]);

    try {
      const result = await postJournalEntry({
        date: new Date(formData.date).getTime(),
        description: formData.description,
        reference: formData.reference || undefined,
        entryType: formData.entryType,
        lines: lines.map(line => ({
          accountId: line.accountId as any,
          debit: line.debit || 0,
          credit: line.credit || 0,
          description: line.description || undefined
        })),
        createdBy: 'current-user' // This would come from auth context
      });

      // Show success message and navigate
      alert(`Journal entry ${result.entryNumber} created successfully!`);
      navigate('/finance/journal-entries');
    } catch (error) {
      console.error('Error creating journal entry:', error);
      setErrors([`Error creating entry: ${(error as Error).message}`]);
    } finally {
      setIsSubmitting(false);
    }
  };

  const { totalDebits, totalCredits, difference } = calculateTotals();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">New Journal Entry</h2>
          <p className="text-gray-600">Record a new financial transaction</p>
        </div>
        <button
          onClick={() => navigate('/finance/journal-entries')}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="text-red-400">⚠️</div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Please fix the following errors:</h3>
              <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Entry Details */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Entry Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Reference</label>
              <input
                type="text"
                value={formData.reference}
                onChange={(e) => setFormData(prev => ({ ...prev, reference: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Invoice #, Check #, etc."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Entry Type</label>
              <select
                value={formData.entryType}
                onChange={(e) => setFormData(prev => ({ ...prev, entryType: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="regular">Regular</option>
                <option value="adjusting">Adjusting</option>
                <option value="closing">Closing</option>
                <option value="reversing">Reversing</option>
              </select>
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Describe the transaction..."
              required
            />
          </div>
        </div>

        {/* Journal Lines */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Journal Lines</h3>
            <button
              type="button"
              onClick={addLine}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              + Add Line
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-2 text-sm font-medium text-gray-700">Account</th>
                  <th className="text-left py-2 text-sm font-medium text-gray-700">Description</th>
                  <th className="text-right py-2 text-sm font-medium text-gray-700">Debit</th>
                  <th className="text-right py-2 text-sm font-medium text-gray-700">Credit</th>
                  <th className="text-center py-2 text-sm font-medium text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody>
                {lines.map((line, index) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-2 pr-4">
                      <select
                        value={line.accountId}
                        onChange={(e) => updateLine(index, 'accountId', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                        required
                      >
                        <option value="">Select account...</option>
                        {flatAccounts.map(account => (
                          <option key={account._id} value={account._id}>
                            {account.displayName}
                          </option>
                        ))}
                      </select>
                    </td>
                    <td className="py-2 pr-4">
                      <input
                        type="text"
                        value={line.description}
                        onChange={(e) => updateLine(index, 'description', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                        placeholder="Line description..."
                      />
                    </td>
                    <td className="py-2 pr-4">
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={line.debit || ''}
                        onChange={(e) => updateLine(index, 'debit', parseFloat(e.target.value) || 0)}
                        className="w-full px-2 py-1 border border-gray-300 rounded text-sm text-right"
                        placeholder="0.00"
                      />
                    </td>
                    <td className="py-2 pr-4">
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={line.credit || ''}
                        onChange={(e) => updateLine(index, 'credit', parseFloat(e.target.value) || 0)}
                        className="w-full px-2 py-1 border border-gray-300 rounded text-sm text-right"
                        placeholder="0.00"
                      />
                    </td>
                    <td className="py-2 text-center">
                      {lines.length > 2 && (
                        <button
                          type="button"
                          onClick={() => removeLine(index)}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          Remove
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="border-t-2 border-gray-300 font-semibold">
                  <td colSpan={2} className="py-2 text-right">Totals:</td>
                  <td className="py-2 text-right">{formatCurrency(totalDebits)}</td>
                  <td className="py-2 text-right">{formatCurrency(totalCredits)}</td>
                  <td className="py-2"></td>
                </tr>
                <tr>
                  <td colSpan={2} className="py-1 text-right text-sm">
                    <span className={`font-medium ${Math.abs(difference) < 0.01 ? 'text-green-600' : 'text-red-600'}`}>
                      {Math.abs(difference) < 0.01 ? '✓ Balanced' : `Out of balance: ${formatCurrency(difference)}`}
                    </span>
                  </td>
                  <td colSpan={3}></td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/finance/journal-entries')}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting || Math.abs(difference) > 0.01}
            className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Creating...' : 'Create Entry'}
          </button>
        </div>
      </form>
    </div>
  );
}

import { Outlet, Link, useLocation } from "react-router";
import { useState } from "react";

const navigation = [
  {
    name: "Dashboard",
    href: "/finance",
    icon: "📊",
    description: "Financial overview and key metrics"
  },
  {
    name: "Chart of Accounts",
    href: "/finance/chart-of-accounts",
    icon: "📋",
    description: "Manage account structure"
  },
  {
    name: "Journal Entries",
    href: "/finance/journal-entries",
    icon: "📝",
    description: "Record transactions"
  },
  {
    name: "General Ledger",
    href: "/finance/general-ledger",
    icon: "📚",
    description: "View account ledgers"
  },
  {
    name: "Trial Balance",
    href: "/finance/trial-balance",
    icon: "⚖️",
    description: "Account balances summary"
  },
  {
    name: "Balance Sheet",
    href: "/finance/balance-sheet",
    icon: "📈",
    description: "Statement of Financial Position"
  },
  {
    name: "Income Statement",
    href: "/finance/income-statement",
    icon: "💰",
    description: "Statement of Comprehensive Income"
  },
  {
    name: "Cash Flow",
    href: "/finance/cash-flow",
    icon: "💸",
    description: "Cash Flow Statement"
  },
  {
    name: "Accounts Payable",
    href: "/finance/accounts-payable",
    icon: "🧾",
    description: "Manage supplier invoices"
  },
  {
    name: "Cash & Bank",
    href: "/finance/cash-bank",
    icon: "🏦",
    description: "Cash and bank management"
  },
  {
    name: "Reports",
    href: "/finance/reports",
    icon: "📊",
    description: "Financial reports and snapshots"
  },
];

export default function FinanceLayout() {
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const isActive = (href: string) => {
    if (href === "/finance") {
      return location.pathname === "/finance";
    }
    return location.pathname.startsWith(href);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:inset-0
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <div className="flex items-center">
              <span className="text-2xl font-bold text-blue-600">💼</span>
              <span className="ml-2 text-lg font-semibold text-gray-900">Finance</span>
            </div>
            <button
              className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500"
              onClick={() => setSidebarOpen(false)}
            >
              ✕
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`
                  group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors
                  ${isActive(item.href)
                    ? 'bg-blue-100 text-blue-900 border-r-2 border-blue-500'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }
                `}
                title={item.description}
              >
                <span className="mr-3 text-lg">{item.icon}</span>
                <div className="flex-1">
                  <div className="font-medium">{item.name}</div>
                  <div className="text-xs text-gray-500 mt-0.5">{item.description}</div>
                </div>
              </Link>
            ))}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500">
              <div className="font-medium">XYZ Manufacturing ERP</div>
              <div>Finance & Accounting Module</div>
              <div className="mt-1 text-green-600">✓ PFRS Compliant</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-4">
            <div className="flex items-center">
              <button
                className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500"
                onClick={() => setSidebarOpen(true)}
              >
                ☰
              </button>
              <nav className="hidden lg:flex space-x-2 text-sm text-gray-500">
                <Link to="/" className="hover:text-gray-700">Home</Link>
                <span>/</span>
                <span className="text-gray-900 font-medium">Finance</span>
                {location.pathname !== "/finance" && (
                  <>
                    <span>/</span>
                    <span className="text-gray-900 font-medium capitalize">
                      {location.pathname.split('/').pop()?.replace('-', ' ')}
                    </span>
                  </>
                )}
              </nav>
            </div>

            <div className="flex items-center space-x-4">
              {/* Quick actions */}
              <Link
                to="/finance/journal-entries/new"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                <span className="mr-1">+</span>
                New Entry
              </Link>
              
              {/* User menu placeholder */}
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                👤
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">
          <Outlet />
        </main>
      </div>
    </div>
  );
}

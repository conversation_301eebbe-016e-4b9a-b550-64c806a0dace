// Utility functions for accounting calculations and validations

/**
 * Validates that debits equal credits in a journal entry
 */
export function validateDoubleEntry(lines) {
  const totalDebits = lines.reduce((sum, line) => sum + (line.debit || 0), 0);
  const totalCredits = lines.reduce((sum, line) => sum + (line.credit || 0), 0);
  
  return {
    isValid: Math.abs(totalDebits - totalCredits) < 0.01, // Allow for rounding
    totalDebits,
    totalCredits,
    difference: totalDebits - totalCredits
  };
}

/**
 * Generates next journal entry number
 */
export function generateEntryNumber(lastEntryNumber) {
  if (!lastEntryNumber) {
    return "JE-000001";
  }
  
  const match = lastEntryNumber.match(/JE-(\d+)/);
  if (match) {
    const nextNumber = parseInt(match[1]) + 1;
    return `JE-${nextNumber.toString().padStart(6, '0')}`;
  }
  
  return "JE-000001";
}

/**
 * Calculates account balance based on account type and transactions
 */
export function calculateAccountBalance(accountType, openingBalance, debits, credits) {
  const netDebits = debits || 0;
  const netCredits = credits || 0;
  const opening = openingBalance || 0;
  
  // Normal debit balance accounts: Assets, Expenses
  if (accountType === 'asset' || accountType === 'expense') {
    return opening + netDebits - netCredits;
  }
  
  // Normal credit balance accounts: Liabilities, Equity, Revenue
  if (accountType === 'liability' || accountType === 'equity' || accountType === 'revenue') {
    return opening + netCredits - netDebits;
  }
  
  return 0;
}

/**
 * Determines if an account has a normal debit or credit balance
 */
export function getNormalBalance(accountType) {
  return ['asset', 'expense'].includes(accountType) ? 'debit' : 'credit';
}

/**
 * Formats currency for Philippine Peso
 */
export function formatCurrency(amount) {
  return new Intl.NumberFormat('en-PH', {
    style: 'currency',
    currency: 'PHP',
    minimumFractionDigits: 2
  }).format(amount);
}

/**
 * Calculates aging buckets for accounts payable
 */
export function calculateAging(dueDate, asOfDate = Date.now()) {
  const daysDiff = Math.floor((asOfDate - dueDate) / (1000 * 60 * 60 * 24));
  
  if (daysDiff < 0) return 'current';
  if (daysDiff <= 30) return '1-30';
  if (daysDiff <= 60) return '31-60';
  if (daysDiff <= 90) return '61-90';
  return 'over-90';
}

/**
 * Validates account code format
 */
export function validateAccountCode(code) {
  // Philippine COA typically uses 4-digit codes
  const pattern = /^\d{4}$/;
  return pattern.test(code);
}

/**
 * Determines account classification for financial statements
 */
export function getAccountClassification(accountType, subtype) {
  const classifications = {
    asset: {
      current_asset: 'Current Assets',
      non_current_asset: 'Non-Current Assets',
      default: 'Assets'
    },
    liability: {
      current_liability: 'Current Liabilities',
      non_current_liability: 'Non-Current Liabilities',
      default: 'Liabilities'
    },
    equity: {
      default: 'Equity'
    },
    revenue: {
      operating_revenue: 'Operating Revenue',
      other_revenue: 'Other Revenue',
      default: 'Revenue'
    },
    expense: {
      cost_of_sales: 'Cost of Sales',
      operating_expense: 'Operating Expenses',
      administrative_expense: 'Administrative Expenses',
      finance_cost: 'Finance Costs',
      other_expense: 'Other Expenses',
      default: 'Expenses'
    }
  };
  
  return classifications[accountType]?.[subtype] || classifications[accountType]?.default || 'Other';
}

/**
 * Rounds amount to 2 decimal places for currency
 */
export function roundCurrency(amount) {
  return Math.round((amount + Number.EPSILON) * 100) / 100;
}

/**
 * Validates date range for financial reports
 */
export function validateDateRange(startDate, endDate) {
  if (startDate && endDate && startDate > endDate) {
    throw new Error("Start date cannot be after end date");
  }
  
  if (endDate > Date.now()) {
    throw new Error("End date cannot be in the future");
  }
  
  return true;
}

/**
 * Gets fiscal year start date (assuming January 1st for Philippines)
 */
export function getFiscalYearStart(date) {
  const year = new Date(date).getFullYear();
  return new Date(year, 0, 1).getTime(); // January 1st
}

/**
 * Gets fiscal year end date (assuming December 31st for Philippines)
 */
export function getFiscalYearEnd(date) {
  const year = new Date(date).getFullYear();
  return new Date(year, 11, 31, 23, 59, 59, 999).getTime(); // December 31st
}

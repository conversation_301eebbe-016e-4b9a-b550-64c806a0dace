import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";

interface Account {
  _id: string;
  code: string;
  name: string;
  type: string;
  subtype?: string;
  parentAccountId?: string;
  isActive: boolean;
  description?: string;
  children?: Account[];
}

export default function ChartOfAccounts() {
  const [showForm, setShowForm] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  const chartOfAccounts = useQuery(api.chartOfAccounts.getChartOfAccounts, {
    includeInactive: false
  });

  const createAccount = useMutation(api.chartOfAccounts.createAccount);
  const updateAccount = useMutation(api.chartOfAccounts.updateAccount);
  const deleteAccount = useMutation(api.chartOfAccounts.deleteAccount);

  const [formData, setFormData] = useState({
    code: '',
    name: '',
    type: 'asset' as const,
    subtype: '',
    parentAccountId: '',
    description: ''
  });

  const accountTypes = [
    { value: 'asset', label: 'Asset' },
    { value: 'liability', label: 'Liability' },
    { value: 'equity', label: 'Equity' },
    { value: 'revenue', label: 'Revenue' },
    { value: 'expense', label: 'Expense' }
  ];

  const subtypeOptions = {
    asset: [
      { value: 'current_asset', label: 'Current Asset' },
      { value: 'non_current_asset', label: 'Non-Current Asset' }
    ],
    liability: [
      { value: 'current_liability', label: 'Current Liability' },
      { value: 'non_current_liability', label: 'Non-Current Liability' }
    ],
    equity: [],
    revenue: [
      { value: 'operating_revenue', label: 'Operating Revenue' },
      { value: 'other_revenue', label: 'Other Revenue' }
    ],
    expense: [
      { value: 'cost_of_sales', label: 'Cost of Sales' },
      { value: 'operating_expense', label: 'Operating Expense' },
      { value: 'administrative_expense', label: 'Administrative Expense' },
      { value: 'finance_cost', label: 'Finance Cost' },
      { value: 'other_expense', label: 'Other Expense' }
    ]
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingAccount) {
        await updateAccount({
          accountId: editingAccount._id,
          name: formData.name,
          parentAccountId: formData.parentAccountId || undefined,
          description: formData.description || undefined
        });
      } else {
        await createAccount({
          code: formData.code,
          name: formData.name,
          type: formData.type,
          subtype: formData.subtype || undefined,
          parentAccountId: formData.parentAccountId || undefined,
          description: formData.description || undefined
        });
      }
      resetForm();
    } catch (error) {
      console.error('Error saving account:', error);
      alert('Error saving account: ' + (error as Error).message);
    }
  };

  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      type: 'asset',
      subtype: '',
      parentAccountId: '',
      description: ''
    });
    setShowForm(false);
    setEditingAccount(null);
  };

  const handleEdit = (account: Account) => {
    setEditingAccount(account);
    setFormData({
      code: account.code,
      name: account.name,
      type: account.type as any,
      subtype: account.subtype || '',
      parentAccountId: account.parentAccountId || '',
      description: account.description || ''
    });
    setShowForm(true);
  };

  const handleDelete = async (account: Account) => {
    if (confirm(`Are you sure you want to delete account "${account.name}"?`)) {
      try {
        await deleteAccount({ accountId: account._id });
      } catch (error) {
        alert('Error deleting account: ' + (error as Error).message);
      }
    }
  };

  const toggleExpanded = (accountId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(accountId)) {
      newExpanded.delete(accountId);
    } else {
      newExpanded.add(accountId);
    }
    setExpandedNodes(newExpanded);
  };

  const renderAccountTree = (accounts: Account[], level = 0) => {
    return accounts.map((account) => (
      <div key={account._id} className={`${level > 0 ? 'ml-6' : ''}`}>
        <div className="flex items-center justify-between py-2 px-3 hover:bg-gray-50 rounded-md group">
          <div className="flex items-center flex-1">
            {account.children && account.children.length > 0 && (
              <button
                onClick={() => toggleExpanded(account._id)}
                className="mr-2 p-1 hover:bg-gray-200 rounded"
              >
                {expandedNodes.has(account._id) ? '▼' : '▶'}
              </button>
            )}
            <div className="flex-1">
              <div className="flex items-center space-x-3">
                <span className="font-mono text-sm text-gray-600">{account.code}</span>
                <span className="font-medium text-gray-900">{account.name}</span>
                <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                  {account.type}
                </span>
                {account.subtype && (
                  <span className="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded-full">
                    {account.subtype.replace('_', ' ')}
                  </span>
                )}
              </div>
              {account.description && (
                <div className="text-sm text-gray-500 mt-1">{account.description}</div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={() => handleEdit(account)}
              className="p-1 text-blue-600 hover:bg-blue-100 rounded"
              title="Edit account"
            >
              ✏️
            </button>
            <button
              onClick={() => handleDelete(account)}
              className="p-1 text-red-600 hover:bg-red-100 rounded"
              title="Delete account"
            >
              🗑️
            </button>
          </div>
        </div>
        {account.children && account.children.length > 0 && expandedNodes.has(account._id) && (
          <div className="ml-4 border-l border-gray-200">
            {renderAccountTree(account.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  const getAllAccounts = (accounts: Account[]): Account[] => {
    const result: Account[] = [];
    const traverse = (accs: Account[]) => {
      accs.forEach(acc => {
        result.push(acc);
        if (acc.children) traverse(acc.children);
      });
    };
    traverse(accounts);
    return result;
  };

  const potentialParents = chartOfAccounts 
    ? getAllAccounts(chartOfAccounts).filter(acc => 
        acc.type === formData.type && (!editingAccount || acc._id !== editingAccount._id)
      )
    : [];

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Chart of Accounts</h1>
          <p className="text-gray-600">Manage your account structure</p>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <span className="mr-2">+</span>
          Add Account
        </button>
      </div>

      {/* Account Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingAccount ? 'Edit Account' : 'Add New Account'}
              </h3>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Account Code</label>
                  <input
                    type="text"
                    value={formData.code}
                    onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., 1001"
                    required
                    disabled={!!editingAccount}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Account Name</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="e.g., Cash in Bank"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Account Type</label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any, subtype: '' }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    disabled={!!editingAccount}
                  >
                    {accountTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                {subtypeOptions[formData.type].length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Subtype</label>
                    <select
                      value={formData.subtype}
                      onChange={(e) => setFormData(prev => ({ ...prev, subtype: e.target.value }))}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    >
                      <option value="">Select subtype (optional)</option>
                      {subtypeOptions[formData.type].map(subtype => (
                        <option key={subtype.value} value={subtype.value}>{subtype.label}</option>
                      ))}
                    </select>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700">Parent Account</label>
                  <select
                    value={formData.parentAccountId}
                    onChange={(e) => setFormData(prev => ({ ...prev, parentAccountId: e.target.value }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="">No parent (top level)</option>
                    {potentialParents.map(account => (
                      <option key={account._id} value={account._id}>
                        {account.code} - {account.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    rows={3}
                    placeholder="Optional description"
                  />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                  >
                    {editingAccount ? 'Update' : 'Create'} Account
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Accounts Tree */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Account Structure</h3>
        </div>
        <div className="p-6">
          {chartOfAccounts ? (
            chartOfAccounts.length > 0 ? (
              <div className="space-y-1">
                {renderAccountTree(chartOfAccounts)}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No accounts found. Create your first account to get started.
              </div>
            )
          ) : (
            <div className="text-center py-8 text-gray-500">Loading accounts...</div>
          )}
        </div>
      </div>
    </div>
  );
}

import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";

export default function BalanceSheet() {
  const [asOfDate, setAsOfDate] = useState(Date.now());
  const [includeZeroBalances, setIncludeZeroBalances] = useState(false);
  const [showComparative, setShowComparative] = useState(false);
  const [priorDate, setPriorDate] = useState(
    new Date(new Date().getFullYear() - 1, new Date().getMonth(), new Date().getDate()).getTime()
  );

  const balanceSheet = useQuery(api.balanceSheet.generateBalanceSheet, {
    asOfDate,
    includeZeroBalances
  });

  const comparativeBalanceSheet = useQuery(
    showComparative ? api.balanceSheet.generateComparativeBalanceSheet : "skip",
    showComparative ? {
      currentDate: asOfDate,
      priorDate,
      includeZeroBalances
    } : "skip"
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-PH');
  };

  const formatPercent = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const renderAccountSection = (accounts: any[], title: string, showTotal = true, totalAmount?: number) => {
    if (!accounts || accounts.length === 0) return null;

    return (
      <div className="mb-6">
        <h4 className="font-semibold text-gray-900 mb-3 text-sm uppercase tracking-wide border-b border-gray-200 pb-1">
          {title}
        </h4>
        <div className="space-y-1">
          {accounts.map((account) => (
            <div key={account.account._id} className="flex justify-between items-center py-1">
              <div className="flex-1">
                <span className="text-sm text-gray-900">{account.account.name}</span>
                <span className="text-xs text-gray-500 ml-2">({account.account.code})</span>
              </div>
              <div className="text-right">
                <span className="text-sm font-mono text-gray-900">
                  {formatCurrency(account.balance)}
                </span>
                {showComparative && comparativeBalanceSheet && (
                  <div className="text-xs text-gray-500">
                    {account.change !== undefined && (
                      <span className={account.change >= 0 ? 'text-green-600' : 'text-red-600'}>
                        {account.change >= 0 ? '+' : ''}{formatCurrency(account.change)}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        {showTotal && totalAmount !== undefined && (
          <div className="flex justify-between items-center py-2 mt-2 border-t border-gray-200 font-semibold">
            <span className="text-sm text-gray-900">Total {title}</span>
            <span className="text-sm font-mono text-gray-900">
              {formatCurrency(totalAmount)}
            </span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Balance Sheet</h1>
          <p className="text-gray-600">Statement of Financial Position</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={showComparative}
              onChange={(e) => setShowComparative(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Comparative</span>
          </label>
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            📊 Export
          </button>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">As of Date</label>
            <input
              type="date"
              value={new Date(asOfDate).toISOString().split('T')[0]}
              onChange={(e) => setAsOfDate(new Date(e.target.value).getTime())}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
          {showComparative && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Prior Date</label>
              <input
                type="date"
                value={new Date(priorDate).toISOString().split('T')[0]}
                onChange={(e) => setPriorDate(new Date(e.target.value).getTime())}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
          )}
          <div className="flex items-end">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={includeZeroBalances}
                onChange={(e) => setIncludeZeroBalances(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">Include zero balances</span>
            </label>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      {balanceSheet && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Total Assets</div>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(balanceSheet.balanceSheet.totals.assets)}
            </div>
            {balanceSheet.ratios.currentRatio > 0 && (
              <div className="text-xs text-gray-500 mt-1">
                Current Ratio: {balanceSheet.ratios.currentRatio.toFixed(2)}
              </div>
            )}
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Total Liabilities</div>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(balanceSheet.balanceSheet.totals.liabilities)}
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Total Equity</div>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(balanceSheet.balanceSheet.totals.equity)}
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Working Capital</div>
            <div className={`text-2xl font-bold ${
              balanceSheet.ratios.workingCapital >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {formatCurrency(balanceSheet.ratios.workingCapital)}
            </div>
            {balanceSheet.ratios.debtToEquityRatio > 0 && (
              <div className="text-xs text-gray-500 mt-1">
                D/E Ratio: {balanceSheet.ratios.debtToEquityRatio.toFixed(2)}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Balance Sheet */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Assets */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 border-b border-gray-200 pb-2">
            ASSETS
          </h3>
          
          {balanceSheet ? (
            <div className="space-y-6">
              {/* Current Assets */}
              {renderAccountSection(
                showComparative && comparativeBalanceSheet 
                  ? comparativeBalanceSheet.comparative.assets.current
                  : balanceSheet.balanceSheet.assets.current,
                "Current Assets",
                true,
                balanceSheet.balanceSheet.assets.totalCurrent
              )}

              {/* Non-Current Assets */}
              {renderAccountSection(
                showComparative && comparativeBalanceSheet 
                  ? comparativeBalanceSheet.comparative.assets.nonCurrent
                  : balanceSheet.balanceSheet.assets.nonCurrent,
                "Non-Current Assets",
                true,
                balanceSheet.balanceSheet.assets.totalNonCurrent
              )}

              {/* Total Assets */}
              <div className="border-t-2 border-gray-300 pt-3">
                <div className="flex justify-between items-center font-bold text-lg">
                  <span className="text-gray-900">TOTAL ASSETS</span>
                  <span className="font-mono text-gray-900">
                    {formatCurrency(balanceSheet.balanceSheet.totals.assets)}
                  </span>
                </div>
                {showComparative && comparativeBalanceSheet && (
                  <div className="flex justify-between items-center text-sm mt-1">
                    <span className="text-gray-500">Change from prior period</span>
                    <span className={`font-mono ${
                      comparativeBalanceSheet.changes.assets >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {comparativeBalanceSheet.changes.assets >= 0 ? '+' : ''}
                      {formatCurrency(comparativeBalanceSheet.changes.assets)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">Loading assets...</div>
          )}
        </div>

        {/* Liabilities and Equity */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 border-b border-gray-200 pb-2">
            LIABILITIES AND EQUITY
          </h3>
          
          {balanceSheet ? (
            <div className="space-y-6">
              {/* Current Liabilities */}
              {renderAccountSection(
                showComparative && comparativeBalanceSheet 
                  ? comparativeBalanceSheet.comparative.liabilities.current
                  : balanceSheet.balanceSheet.liabilities.current,
                "Current Liabilities",
                true,
                balanceSheet.balanceSheet.liabilities.totalCurrent
              )}

              {/* Non-Current Liabilities */}
              {renderAccountSection(
                showComparative && comparativeBalanceSheet 
                  ? comparativeBalanceSheet.comparative.liabilities.nonCurrent
                  : balanceSheet.balanceSheet.liabilities.nonCurrent,
                "Non-Current Liabilities",
                true,
                balanceSheet.balanceSheet.liabilities.totalNonCurrent
              )}

              {/* Total Liabilities */}
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between items-center font-semibold">
                  <span className="text-gray-900">TOTAL LIABILITIES</span>
                  <span className="font-mono text-gray-900">
                    {formatCurrency(balanceSheet.balanceSheet.totals.liabilities)}
                  </span>
                </div>
              </div>

              {/* Equity */}
              <div className="mt-6">
                <h4 className="font-semibold text-gray-900 mb-3 text-sm uppercase tracking-wide border-b border-gray-200 pb-1">
                  EQUITY
                </h4>
                <div className="space-y-1">
                  {(showComparative && comparativeBalanceSheet 
                    ? comparativeBalanceSheet.comparative.equity
                    : balanceSheet.balanceSheet.equity.accounts
                  ).map((account: any) => (
                    <div key={account.account._id} className="flex justify-between items-center py-1">
                      <div className="flex-1">
                        <span className="text-sm text-gray-900">{account.account.name}</span>
                        <span className="text-xs text-gray-500 ml-2">({account.account.code})</span>
                      </div>
                      <span className="text-sm font-mono text-gray-900">
                        {formatCurrency(account.balance)}
                      </span>
                    </div>
                  ))}
                  
                  {/* Retained Earnings */}
                  <div className="flex justify-between items-center py-1">
                    <span className="text-sm text-gray-900">Retained Earnings</span>
                    <span className="text-sm font-mono text-gray-900">
                      {formatCurrency(balanceSheet.balanceSheet.equity.retainedEarnings)}
                    </span>
                  </div>
                </div>
                
                {/* Total Equity */}
                <div className="flex justify-between items-center py-2 mt-2 border-t border-gray-200 font-semibold">
                  <span className="text-sm text-gray-900">TOTAL EQUITY</span>
                  <span className="text-sm font-mono text-gray-900">
                    {formatCurrency(balanceSheet.balanceSheet.totals.equity)}
                  </span>
                </div>
              </div>

              {/* Total Liabilities and Equity */}
              <div className="border-t-2 border-gray-300 pt-3">
                <div className="flex justify-between items-center font-bold text-lg">
                  <span className="text-gray-900">TOTAL LIABILITIES AND EQUITY</span>
                  <span className="font-mono text-gray-900">
                    {formatCurrency(balanceSheet.balanceSheet.totals.liabilitiesAndEquity)}
                  </span>
                </div>
                {showComparative && comparativeBalanceSheet && (
                  <div className="flex justify-between items-center text-sm mt-1">
                    <span className="text-gray-500">Change from prior period</span>
                    <span className={`font-mono ${
                      (comparativeBalanceSheet.changes.liabilities + comparativeBalanceSheet.changes.equity) >= 0 
                        ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {(comparativeBalanceSheet.changes.liabilities + comparativeBalanceSheet.changes.equity) >= 0 ? '+' : ''}
                      {formatCurrency(comparativeBalanceSheet.changes.liabilities + comparativeBalanceSheet.changes.equity)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">Loading liabilities and equity...</div>
          )}
        </div>
      </div>

      {/* Validation and Notes */}
      {balanceSheet?.validation && (
        <div className="space-y-2">
          {balanceSheet.validation.errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="text-red-400">⚠️</div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Balance Sheet Errors:</h3>
                  <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                    {balanceSheet.validation.errors.map((error: string, index: number) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {balanceSheet.summary.isBalanced && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex">
                <div className="text-green-400">✓</div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">
                    Balance Sheet is balanced and complies with the accounting equation.
                  </h3>
                  <p className="text-sm text-green-700 mt-1">
                    Assets = Liabilities + Equity ({formatCurrency(balanceSheet.balanceSheet.totals.assets)})
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Report Footer */}
      <div className="bg-gray-50 rounded-lg p-4 text-center text-sm text-gray-600">
        <div>XYZ Manufacturing Corporation</div>
        <div>Statement of Financial Position</div>
        <div>As of {formatDate(asOfDate)}</div>
        {showComparative && (
          <div>Comparative with {formatDate(priorDate)}</div>
        )}
        <div className="mt-2 text-xs">
          Generated on {new Date().toLocaleString('en-PH')} | Philippine GAAP (PFRS) Compliant
        </div>
      </div>
    </div>
  );
}

import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { Link } from "react-router";
import { useState } from "react";

export default function JournalEntriesList() {
  const [dateFilter, setDateFilter] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).getTime(), // Start of month
    endDate: Date.now()
  });

  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Get general ledger data which includes all journal entries
  const generalLedger = useQuery(api.generalLedger.getGeneralLedger, {
    startDate: dateFilter.startDate,
    endDate: dateFilter.endDate
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-PH');
  };

  const formatDateTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('en-PH');
  };

  // Extract unique journal entries from the general ledger
  const journalEntries = generalLedger?.ledgers.reduce((entries: any[], ledger) => {
    ledger.entries.forEach((entry: any) => {
      const existingEntry = entries.find(e => e.journalEntry.entryNumber === entry.journalEntry.entryNumber);
      if (!existingEntry) {
        entries.push({
          ...entry.journalEntry,
          totalDebit: 0,
          totalCredit: 0,
          lineCount: 0
        });
      }
    });
    return entries;
  }, []) || [];

  // Calculate totals for each entry
  journalEntries.forEach(entry => {
    const entryLines = generalLedger?.ledgers.reduce((lines: any[], ledger) => {
      return lines.concat(ledger.entries.filter((e: any) => 
        e.journalEntry.entryNumber === entry.entryNumber
      ));
    }, []) || [];

    entry.totalDebit = entryLines.reduce((sum: number, line: any) => sum + line.debit, 0);
    entry.totalCredit = entryLines.reduce((sum: number, line: any) => sum + line.credit, 0);
    entry.lineCount = entryLines.length;
  });

  // Sort by date (newest first)
  journalEntries.sort((a, b) => b.date - a.date);

  // Filter by status if needed
  const filteredEntries = statusFilter === 'all' 
    ? journalEntries 
    : journalEntries.filter(entry => entry.status === statusFilter);

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
            <input
              type="date"
              value={new Date(dateFilter.startDate).toISOString().split('T')[0]}
              onChange={(e) => setDateFilter(prev => ({ 
                ...prev, 
                startDate: new Date(e.target.value).getTime() 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
            <input
              type="date"
              value={new Date(dateFilter.endDate).toISOString().split('T')[0]}
              onChange={(e) => setDateFilter(prev => ({ 
                ...prev, 
                endDate: new Date(e.target.value).getTime() 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Statuses</option>
              <option value="draft">Draft</option>
              <option value="posted">Posted</option>
              <option value="reversed">Reversed</option>
            </select>
          </div>
          <div className="flex items-end">
            <Link
              to="/finance/journal-entries/new"
              className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <span className="mr-2">+</span>
              New Entry
            </Link>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      {generalLedger && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Total Entries</div>
            <div className="text-2xl font-bold text-gray-900">{filteredEntries.length}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Total Debits</div>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(filteredEntries.reduce((sum, entry) => sum + entry.totalDebit, 0))}
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Total Credits</div>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(filteredEntries.reduce((sum, entry) => sum + entry.totalCredit, 0))}
            </div>
          </div>
        </div>
      )}

      {/* Journal Entries Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Journal Entries</h3>
        </div>
        
        {generalLedger ? (
          filteredEntries.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Entry #
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reference
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Debit
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Credit
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredEntries.map((entry) => (
                    <tr key={entry.entryNumber} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Link
                          to={`/finance/journal-entries/${entry._id || entry.entryNumber}`}
                          className="text-blue-600 hover:text-blue-900 font-medium"
                        >
                          {entry.entryNumber}
                        </Link>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(entry.date)}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        <div className="max-w-xs truncate" title={entry.description}>
                          {entry.description}
                        </div>
                        <div className="text-xs text-gray-500">
                          {entry.lineCount} line{entry.lineCount !== 1 ? 's' : ''}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {entry.reference || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-mono">
                        {formatCurrency(entry.totalDebit)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-mono">
                        {formatCurrency(entry.totalCredit)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <span className={`
                          inline-flex px-2 py-1 text-xs font-semibold rounded-full
                          ${entry.status === 'posted' 
                            ? 'bg-green-100 text-green-800' 
                            : entry.status === 'draft'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                          }
                        `}>
                          {entry.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                        <Link
                          to={`/finance/journal-entries/${entry._id || entry.entryNumber}`}
                          className="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          View
                        </Link>
                        {entry.status === 'posted' && (
                          <button
                            className="text-red-600 hover:text-red-900"
                            title="Reverse Entry"
                          >
                            Reverse
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500 mb-4">No journal entries found for the selected criteria</div>
              <Link
                to="/finance/journal-entries/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <span className="mr-2">+</span>
                Create First Entry
              </Link>
            </div>
          )
        ) : (
          <div className="text-center py-12 text-gray-500">
            Loading journal entries...
          </div>
        )}
      </div>
    </div>
  );
}
